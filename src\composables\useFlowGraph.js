import { ref, onUnmounted, reactive, watchEffect } from 'vue';
import { Graph } from '@antv/x6';
import { <PERSON>roller } from '@antv/x6-plugin-scroller';
import { MiniMap } from '@antv/x6-plugin-minimap';
import { Snapline } from '@antv/x6-plugin-snapline';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { History } from '@antv/x6-plugin-history';
import { Transform } from '@antv/x6-plugin-transform';
import { Stencil } from '@antv/x6-plugin-stencil';
import { Export } from '@antv/x6-plugin-export';
import { Clipboard } from '@antv/x6-plugin-clipboard';
import { Selection } from '@antv/x6-plugin-selection';
import { registerCustomNodes } from '../utils/flowUtils';

// 创建单例状态
const sharedState = {
  graph: reactive({
    instance: null,
    elements: [],
    nodes: [],
    edges: [],
    history: {
      past: [],
      future: []
    },
    selected: null,
    viewport: {
      x: 0,
      y: 0,
      zoom: 1
    },
    // 画布锁定状态
    locked: false
  }),
  canUndo: ref(false),
  canRedo: ref(false),
  //选中插件
  SelectPlugin: ref(null),
};

/**
 * 流程图状态管理组合式函数
 * 负责图形实例的创建、管理和销毁
 * 使用单例模式确保所有组件使用同一个实例
 */
export function useFlowGraph () {
  // 使用共享状态
  const { graph, canUndo, canRedo, SelectPlugin } = sharedState;

  const stencil = ref(null);

  // 初始化流程图
  const initGraph = (containerEl) => {
    if (!containerEl) return null;

    console.log('开始初始化流程图...');

    // 如果已经初始化过，直接返回实例
    if (graph.instance) {
      console.log('流程图已经初始化过，直接返回实例');
      return graph.instance;
    }

    // 注册自定义节点
    registerCustomNodes();

    // 创建画布
    // 创建X6流程图实例，并进行详细配置
    graph.instance = new Graph({
      // 挂载的容器元素
      container: containerEl,
      // 画布宽度和高度自适应容器
      width: containerEl.clientWidth,
      height: containerEl.clientHeight,
      // 设置画布背景色
      background: {
        color: '#f5f7fa',
      },
      autoResize: true, //自适应画布大小
      // 配置网格
      grid: {
        visible: true,      // 显示网格
        type: 'dot',        // 点状网格
        size: 10,           // 网格间距
        color: '#e0e0e0',   // 网格颜色
      },
      // 连线相关配置
      connecting: {
        router: 'manhattan', // 路由类型：直角折线
        connector: {
          name: 'rounded',   // 连线样式：圆角
          args: {
            radius: 8,       // 圆角半径
          },
        },
        anchor: 'center',         // 锚点位置
        connectionPoint: 'boundary', // 连接点类型
        allowBlank: false,        // 不允许连接到空白处
        snap: {
          radius: 20,             // 吸附半径
        },
        // 创建新边时的默认配置
        createEdge () {
          return graph.instance.createEdge({
            attrs: {
              line: {
                stroke: '#5F95FF',      // 边颜色
                strokeWidth: 2,         // 边宽度
                targetMarker: {
                  name: 'classic',      // 终点箭头样式
                  size: 8,              // 箭头大小
                },
              },
            },
            zIndex: 0,
            tools: [
              {
                name: 'button-remove',  // 边上显示删除按钮
                args: {
                  distance: 20
                }
              }
            ]
          });
        },
        // 校验连接是否合法
        validateConnection ({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          // 禁止自身连接
          if (sourceView === targetView) {
            return false;
          }
          // 必须有有效的起点和终点磁铁
          if (!sourceMagnet) {
            return false;
          }
          if (!targetMagnet) {
            return false;
          }
          return true;
        },
      },
      // 高亮配置
      highlighting: {
        // 可用磁铁高亮
        magnetAvailable: {
          name: 'stroke',
          args: {
            attrs: {
              stroke: '#52c41a',
              strokeWidth: 4,
            },
          },
        },
        // 吸附时高亮
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              stroke: '#1890ff',
              strokeWidth: 4,
            },
          }
        },
      },
      // 鼠标滚轮缩放配置
      mousewheel: {
        enabled: true,                // 启用缩放
        zoomAtMousePosition: true,    // 以鼠标为中心缩放
        modifiers: 'ctrl',            // 需按住ctrl
        minScale: 0.5,                // 最小缩放
        maxScale: 3,                  // 最大缩放
      },
      // 交互行为配置
      interacting: {
        nodeMovable: true,            // 节点可拖动
        edgeMovable: true,            // 边可拖动
        edgeLabelMovable: false,      // 禁用边标签拖动
        arrowheadMovable: true,       // 箭头可拖动
        vertexMovable: false,         // 禁用顶点拖动
        vertexAddable: false,         // 禁用添加顶点
        vertexDeletable: false,       // 禁用删除顶点
        magnetConnectable: true,      // 端口可连接
        toolsAddable: true,           // 可添加工具
        useEdgeTools: true,           // 启用边工具
      },
      // 画布平移配置
      panning: {
        enabled: true,                // 启用平移
        modifiers: 'shift',           // 需按住shift键才能拖动画布
        eventTypes: ['leftMouseDown'], // 只响应鼠标左键
      },
    });

    // 应用插件
    applyPlugins();

    // 设置键盘快捷键
    setupKeyboardShortcuts();

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      if (graph.instance) {
        graph.instance.resize(
          containerEl.clientWidth,
          containerEl.clientHeight
        );
      }
    });

    // 监听流程图变化
    if (graph.instance) {
      // 监听历史记录相关事件
      graph.instance.on('history:change', updateHistoryState);
      graph.instance.on('history:undo', () => updateHistoryState());
      graph.instance.on('history:redo', () => updateHistoryState());

      // 监听边的事件
      graph.instance.on('edge:mouseenter', ({ edge }) => {
        graph.currentEdgeId = edge.id;
      });

      graph.instance.on('edge:mouseleave', () => {
        graph.currentEdgeId = null;
      });

      // 监听边的vertices变化事件
      graph.instance.on('edge:change:vertices', ({ edge, current }) => {
        if (current && Array.isArray(current)) {
          // 保存vertices信息
          edge.vertices = current;
        }
      });
    }

    console.log('流程图初始化完成');
    updateHistoryState();
    return graph.instance;
  };

  // 应用X6插件
  const applyPlugins = () => {
    if (!graph.instance) return;

    // 滚动画布插件
    graph.instance.use(
      new Scroller({
        enabled: true,
        pannable: false,  // 禁用普通拖动
        autoResize: true,
        pageVisible: false,
        pageBreak: false,
      })
    );

    // 对齐线插件
    graph.instance.use(
      new Snapline({
        enabled: true,
        sharp: true,
      })
    );
    // 键盘插件
    graph.instance.use(
      new Keyboard({
        enabled: true,
        global: false,
      })
    );

    // 撤销重做插件
    graph.instance.use(
      new History({
        enabled: true,
        stackSize: 100,
        beforeAddCommand: (event, args) => {
          // 过滤掉与工具相关的操作，不将其添加到历史记录中
          if (event === 'cell:change:tools') {
            return false;
          }
          // 如果操作明确指定不记录历史，则不添加到历史记录（鼠标悬浮进入 移出时 ）
          if (args && args.options && args.options.recordHistory === false) {
            return false;
          }
          return true;
        }
      })
    );

    // 剪贴板插件
    graph.instance.use(
      new Clipboard({
        enabled: true,
      })
    );

    // 选择插件 - 只允许选择节点，不允许选择边缘线
    SelectPlugin.value = new Selection({
      enabled: true,
      multiple: true,
      rubberband: true,
      movable: true,
      showNodeSelectionBox: true,
      showEdgeSelectionBox: false, // 不显示边缘线选择框
      // 过滤器：只允许选择节点，不允许选择边缘线
      filter: (cell) => {
        // 如果是节点，则允许选择；如果是边缘线，则不允许选择
        return cell.isNode && cell.isNode();
      },
    })
    graph.instance.use(
      SelectPlugin.value
    );

    // 变换插件（调整大小、旋转等）
    graph.instance.use(
      new Transform({
        resizing: {
          enabled: true,
          minWidth: 1,
          minHeight: 1,
          maxWidth: 1000,
          maxHeight: 1000,
          orthogonal: false,
          restrict: false,
          preserveAspectRatio: false,
          onResizing: (args) => {
            // 调整大小过程中不记录历史
            if (args?.e?.options) {
              args.e.options = { ...args.e.options, recordHistory: false };
            }
            if (args?.options) {
              args.options = { ...args.options, recordHistory: false };
            }
          },
          onResized: (args) => {
            try {
              const { node, width, height } = args || {};
              if (node && typeof node.resize === 'function' && width !== undefined && height !== undefined) {
                node.resize(width, height, { recordHistory: false });
              }
            } catch (error) {
              console.error('onResized 错误:', error);
            } finally {
              // 保存历史记录

            }
          },
        },
        rotating: {
          enabled: true,
          grid: 15,
          onRotating: (args) => {
            // 旋转过程中不记录历史
            if (args?.e?.options) {
              args.e.options = { ...args.e.options, recordHistory: false };
            }
            if (args?.options) {
              args.options = { ...args.options, recordHistory: false };
            }
          },
          onRotated: (args) => {
            try {
              const { node, angle } = args || {};
              if (node && typeof node.rotate === 'function' && angle !== undefined) {
                node.rotate(angle, { recordHistory: false });
              }
            } catch (error) {
              console.error('onRotated 错误:', error);
            } finally {
              // 保存历史记录
            }
          },
        },
      })
    );

    // 只有在非锁定状态下才确保交互功能正常
    if (graph.instance.options?.interacting && !graph.locked) {
      // 启用必要的交互功能，禁用vertices相关功能（仅在非锁定状态下）
      graph.instance.options.interacting.nodeMovable = true;
      graph.instance.options.interacting.edgeMovable = true;
      graph.instance.options.interacting.arrowheadMovable = true;
      graph.instance.options.interacting.vertexMovable = false;
      graph.instance.options.interacting.vertexAddable = false;
      graph.instance.options.interacting.vertexDeletable = false;
    }
  };

  // 初始化节点模板库
  const initStencil = (stencilContainer, nodes) => {
    if (!graph.instance || !stencilContainer || !nodes || nodes.length === 0) return;

    // 初始化Stencil插件
    stencil.value = new Stencil({
      title: '流程图节点库',
      target: graph.instance,
      search: (cell, keyword) => {
        if (!keyword || keyword.trim() === '') return true;
        return cell.label && cell.label.toLowerCase().includes(keyword.toLowerCase());
      },
      placeholder: '搜索节点',
      notFoundText: '未找到节点',
      collapsable: false,
      stencilGraphWidth: 320,
      stencilGraphHeight: 450,
      groups: [
        {
          name: 'basic',
          title: '基础节点',
          collapsable: false,
        }
      ],
      layoutOptions: {
        columns: 2,
        columnWidth: 140,
        rowHeight: 100,
        marginX: 15,
        marginY: 25,
        resizeToFit: true,
        center: true,
      },
    });

    // 挂载节点模板库
    stencilContainer.appendChild(stencil.value.container);

    // 添加基础节点到模板库
    const basicNodes = nodes.filter(node => node.group === 'basic').map(node => {
      return graph.instance.createNode({
        shape: node.shape,
        width: node.width,
        height: node.height,
        label: node.label,
        attrs: {
          ...(node.attrs || {}),
          body: {
            stroke: node.attrs?.body?.stroke || '#5F95FF',
            strokeWidth: node.attrs?.body?.strokeWidth || 2,
            fill: node.attrs?.body?.fill || '#EFF7FF',
            rx: node.attrs?.body?.rx || 0,
            ry: node.attrs?.body?.ry || 0,
            refPoints: node.attrs?.body?.refPoints,
            // 移除阴影效果
            filter: 'none',
          },
          text: {
            fontSize: node.attrs?.text?.fontSize || 14,
            fontWeight: 500,
            fill: node.attrs?.text?.fill || '#333333',
            textWrap: {
              width: node.width - 20,
              height: node.height - 10,
              ellipsis: true,
            },
            textAnchor: 'middle',
            textVerticalAnchor: 'middle',
          },
        },
        ports: node.ports,
      });
    });

    // 加载节点到模板库
    stencil.value.load(basicNodes, 'basic');
  };

  // 初始化小地图
  const initMinimap = (minimapContainer) => {
    if (!graph.instance || !minimapContainer) return;

    // 创建小地图插件
    const minimap = new MiniMap({
      width: 200,
      height: 160,
      padding: 10,
    });

    // 应用小地图插件
    graph.instance.use(minimap);

    // 设置小地图容器
    minimapContainer.appendChild(minimap.container);
  };

  /**
   *
   * 支持的快捷键包括:
   * - 复制: Ctrl+C / Command+C
   * - 剪切: Ctrl+X / Command+X
   * - 粘贴: Ctrl+V / Command+V
   * - 撤销: Ctrl+Z / Command+Z
   * - 重做: Ctrl+Y / Shift+Ctrl+Z / Shift+Command+Z
   * - 删除: Delete / Backspace
   * - 全选: Ctrl+A / Command+A
   */

  const setupKeyboardShortcuts = () => {
    if (!graph.instance) return;
    graph.instance.bindKey(['ctrl+z', 'command+z'], () => {
      undo();
      return false;
    });

    // 复制 - Ctrl+C / Command+C
    graph.instance.bindKey(['ctrl+c', 'command+c'], () => {
      const cells = graph.instance.getSelectedCells();
      if (cells.length) {
        graph.instance.copy(cells);
      }
      return false;
    });

    // 剪切 - Ctrl+X / Command+X
    graph.instance.bindKey(['ctrl+x', 'command+x'], () => {
      const cells = graph.instance.getSelectedCells();
      if (cells.length) {
        graph.instance.cut(cells);
      }
      return false;
    });

    // 粘贴 - Ctrl+V / Command+V
    graph.instance.bindKey(['ctrl+v', 'command+v'], () => {
      if (!graph.instance.isClipboardEmpty()) {
        const cells = graph.instance.paste({ offset: 32 });
        graph.instance.cleanSelection();
        graph.instance.select(cells);
      }
      return false;
    });

    // 重做 - Ctrl+Y / Shift+Ctrl+Z / Shift+Command+Z
    graph.instance.bindKey(['ctrl+y', 'shift+ctrl+z', 'shift+command+z'], () => {
      if (graph.instance.canRedo()) {
        graph.instance.redo();
      }
      return false;
    });

    // 删除 - Delete / Backspace
    graph.instance.bindKey(['delete', 'backspace'], () => {
      const cells = graph.instance.getSelectedCells();
      if (cells.length) {
        graph.instance.removeCells(cells);
      }
    });

    // 全选 - Ctrl+A / Command+A - 只选择节点，不选择边缘线
    graph.instance.bindKey(['ctrl+a', 'command+a'], () => {
      const nodes = graph.instance.getNodes();
      if (nodes && nodes.length > 0) {
        // 只选择节点，不选择边缘线
        graph.instance.select(nodes);
      }
      return false;
    });
  }

  // 销毁流程图
  const disposeGraph = () => {
    if (graph.instance) {
      // 移除事件监听

      // 清理实例
      graph.instance = null;
      graph.elements = [];
      graph.nodes = [];
      graph.edges = [];

      console.log('流程图实例已销毁');
    }
  };

  // 更新图形元素
  const updateElements = (elements) => {

    // 更新元素
    graph.elements = [...elements];
    graph.nodes = elements.filter(el => el.type !== 'edge');
    graph.edges = elements.filter(el => el.type === 'edge');

  };

  // 添加节点
  const addNode = (node) => {
    graph.nodes.push(node);
    graph.elements = [...graph.nodes, ...graph.edges];
  };

  // 添加连线
  const addEdge = (edge) => {
    graph.edges.push(edge);
    graph.elements = [...graph.nodes, ...graph.edges];
  };

  // 移除元素
  const removeElements = (elementsToRemove) => {

    const removeIds = elementsToRemove.map(el => el.id);
    graph.nodes = graph.nodes.filter(node => !removeIds.includes(node.id));
    graph.edges = graph.edges.filter(edge => !removeIds.includes(edge.id));
    graph.elements = [...graph.nodes, ...graph.edges];
  };

  // 清空图形
  const clearGraph = async () => {
    if (!graph.instance) return;

    // 如果画布被锁定，则禁用清空功能
    if (graph.locked) {
      console.log('画布已锁定，无法清空');
      alert('画布已锁定，无法执行清空操作');
      return;
    }

    try {
      // 使用浏览器原生confirm
      const confirmed = window.confirm('确定要清空当前流程图吗？此操作不可恢复');

      if (confirmed) {
        // 清空画布
        graph.instance.clearCells();

        // 清空数据
        graph.nodes = [];
        graph.edges = [];
        graph.elements = [];

        // 居中画布
        graph.instance.centerContent();
      }
    } catch (error) {
      console.log('清空操作取消或失败:', error);
    }
  };


  // 撤销操作
  const undo = () => {
    if (graph.instance?.canUndo()) {

      graph.instance.undo();
    }
  };

  // 重做操作
  const redo = () => {
    if (graph.instance?.canRedo()) {
      graph.instance.redo();
    }
  };

  // 更新历史记录状态
  const updateHistoryState = () => {
    if (graph.instance) {
      canUndo.value = graph.instance.canUndo();
      canRedo.value = graph.instance.canRedo();
    } else {
      canUndo.value = false;
      canRedo.value = false;
    }
  };

  // 获取流程图JSON数据
  const getGraphJson = () => {
    return JSON.stringify({
      nodes: graph.nodes,
      edges: graph.edges
    }, null, 2);
  };

  // 加载流程图数据
  const loadGraphData = (data) => {
    try {
      const graphData = typeof data === 'string' ? JSON.parse(data) : data;
      graph.nodes = graphData.nodes || [];
      graph.edges = graphData.edges || [];
      graph.elements = [...graph.nodes, ...graph.edges];

      console.log('流程图数据加载成功');
    } catch (error) {
      console.error('加载流程图数据失败:', error);
    }
  };


  // 监视历史记录状态变化
  watchEffect(() => {
    updateHistoryState();
  });

  // 画布缩放相关方法
  const zoomIn = () => graph.instance?.zoomTo(Math.min((graph.instance?.zoom() || 1) + 0.1, 2));

  const zoomOut = () => graph.instance?.zoomTo(Math.max((graph.instance?.zoom() || 1) - 0.1, 0.5));

  const zoomReset = () => graph.instance?.zoomTo(1);

  const centerContent = () => graph.instance?.centerContent();

  // 导出为图片
  const exportImage = async () => {
    if (!graph.instance) return null;

    try {
      const exporter = new Export({ backgroundColor: '#ffffff' });
      graph.instance.use(exporter);
      return await graph.instance.exportPNG();
    } catch (error) {
      console.error('导出图片失败:', error);
      return null;
    }
  };

  // 导出为JSON
  const exportToJSON = () => {
    if (!graph.instance) {
      console.log('graph.instance为null，无法导出JSON数据');
      return { nodes: [], edges: [] };
    }

    try {
      // 获取画布数据
      const jsonData = graph.instance.toJSON();

      if (!jsonData.cells || jsonData.cells.length === 0) {
        return { nodes: [], edges: [] };
      }

      // 处理节点数据
      const nodes = jsonData.cells
        .filter(cell => cell.shape !== 'edge')
        .map(node => {
          const nodeData = JSON.parse(JSON.stringify(node));

          // 清理冗余属性
          ['x', 'y', 'width', 'height', 'tools'].forEach(key => {
            if (nodeData[key] !== undefined) delete nodeData[key];
          });

          // 简化连接桩信息
          if (nodeData.ports && nodeData.ports.groups) {
            const portItems = nodeData.ports.items || [];
            nodeData.ports = {
              items: portItems.map(item => ({
                id: item.id,
                group: item.group
              }))
            };
          }

          // 获取节点自定义数据
          try {
            const nodeInstance = graph.instance.getCellById(node.id);
            if (nodeInstance) {
              nodeData.data = nodeInstance.getData() || nodeInstance.roleConfig;
            }
          } catch (err) {
            console.warn(`获取节点 ${node.id} 数据失败`);
          }

          return nodeData;
        });

      // 处理边数据
      const edges = jsonData.cells
        .filter(cell => cell.shape === 'edge')
        .map(edge => {
          const edgeData = JSON.parse(JSON.stringify(edge));

          // 移除无用字段
          if (edgeData.tools) delete edgeData.tools;

          return edgeData;
        });

      const result = { nodes, edges };

      // 更新本地状态
      if (nodes.length > 0 || edges.length > 0) {
        graph.nodes = nodes;
        graph.edges = edges;
        graph.elements = [...nodes, ...edges];
      }

      return result;
    } catch (error) {
      console.error('导出JSON数据失败:', error);
      return { nodes: [], edges: [] };
    }
  };

  // 导入JSON数据
  const importJSON = (jsonData) => {
    if (!graph.instance) return false;

    try {
      // 清空现有画布
      graph.instance.clearCells();

      const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;

      if (!data.nodes || !Array.isArray(data.nodes)) {
        console.error('导入的JSON数据格式不正确');
        return false;
      }

      // 处理节点数据
      const processedNodes = data.nodes.map(node => {
        const nodeData = JSON.parse(JSON.stringify(node));

        // 确保节点位置和尺寸属性
        if (!nodeData.position) {
          nodeData.position = {
            x: nodeData.x !== undefined ? nodeData.x : 100,
            y: nodeData.y !== undefined ? nodeData.y : 100
          };
        }

        if (!nodeData.size) {
          nodeData.size = {
            width: nodeData.width !== undefined ? nodeData.width : 100,
            height: nodeData.height !== undefined ? nodeData.height : 60
          };
        }

        // 处理标签文本
        if (nodeData.label && (!nodeData.attrs?.text?.text)) {
          if (!nodeData.attrs) nodeData.attrs = {};
          if (!nodeData.attrs.text) nodeData.attrs.text = {};
          nodeData.attrs.text.text = nodeData.label;
        }

        return nodeData;
      });

      // 处理边数据
      const processedEdges = data.edges.map(edge => {
        const edgeData = JSON.parse(JSON.stringify(edge));

        // 确保 source 和 target 格式正确
        if (typeof edgeData.source === 'string') {
          edgeData.source = { cell: edgeData.source };
        }
        if (typeof edgeData.target === 'string') {
          edgeData.target = { cell: edgeData.target };
        }

        // 处理标签信息
        if (edgeData.label && (!edgeData.labels || edgeData.labels.length === 0)) {
          edgeData.labels = [{
            attrs: {
              label: {
                text: edgeData.label,
                fill: edgeData.labelStyle?.fill || '#333333',
                fontSize: edgeData.labelStyle?.fontSize || 12
              }
            },
            position: { distance: 0.5 }
          }];
          delete edgeData.label;
        }

        // 确保边的类型正确
        if (!edgeData.shape) {
          edgeData.shape = 'edge';
        }

        return edgeData;
      });

      // 导入数据到画布
      graph.instance.fromJSON({
        nodes: processedNodes,
        edges: processedEdges
      });

      // 应用节点数据
      processedNodes.forEach(node => {
        if (node.data) {
          const importedNode = graph.instance.getCellById(node.id);
          if (importedNode && typeof importedNode.setData === 'function') {
            importedNode.setData(node.data);
          }
        }
      });

      // 更新内部状态
      const graphJSON = graph.instance.toJSON();
      graph.nodes = graphJSON.cells.filter(cell => cell.shape !== 'edge');
      graph.edges = graphJSON.cells.filter(cell => cell.shape === 'edge');
      graph.elements = [...graph.nodes, ...graph.edges];

      // 自动调整视图以显示所有元素
      graph.instance.centerContent();
      return true;
    } catch (error) {
      console.error('导入JSON数据失败:', error);
      return false;
    }
  };



  // 卸载时清理资源
  onUnmounted(() => {
    if (graph.instance) {
      graph.instance.dispose();
    }
    if (stencil.value) {
      stencil.value.dispose();
    }
  });

  // 更新边缘线样式
  const updateEdgeStyle = (edge, style) => {
    if (!edge || !graph.instance) {
      console.error('更新边缘线样式失败: edge或graph.instance为null');
      return false;
    }

    try {
      // 更新连接器类型
      if (style.lineType) {
        // 根据连接器类型设置参数
        const connectorArgs = {};
        if (style.lineType === 'rounded') connectorArgs.radius = 8;
        else if (style.lineType === 'jumpover') {
          connectorArgs.size = 5;
          connectorArgs.type = 'arc';
        }

        // 设置连接器
        try {
          if (typeof edge.setConnector === 'function') {
            edge.setConnector({
              name: style.lineType,
              args: connectorArgs
            });
          } else {
            edge.connector = { name: style.lineType, args: connectorArgs };
            edge.setSource(edge.getSource());
            edge.setTarget(edge.getTarget());
          }
        } catch (err) {
          edge.attr('line/connector', { name: style.lineType, args: connectorArgs });
        }
      }

      // 更新线条样式
      edge.attr('line/stroke', style.stroke);
      edge.attr('line/strokeWidth', style.strokeWidth);
      edge.attr('line/strokeDasharray', style.strokeDasharray || '');

      // 更新箭头
      const updateMarker = (position, markerName) => {
        if (markerName && markerName !== 'none') {
          edge.attr(`line/${position}Marker`, {
            name: markerName,
            size: style.markerSize
          });
        } else {
          edge.attr(`line/${position}Marker`, null);
        }
      };

      updateMarker('source', style.sourceMarker);
      updateMarker('target', style.targetMarker);

      // 更新本地状态
      const jsonData = graph.instance.toJSON();
      if (jsonData.cells) {
        graph.nodes = jsonData.cells.filter(cell => cell.shape !== 'edge');
        graph.edges = jsonData.cells.filter(cell => cell.shape === 'edge');
        graph.elements = [...graph.nodes, ...graph.edges];
      }

      return true;
    } catch (error) {
      console.error('更新边缘线样式失败:', error);
      return false;
    }
  };

  // 更新节点样式
  const updateNodeStyle = (node, style) => {
    if (!node || !graph.instance) {
      console.error('更新节点样式失败: node或graph.instance为null');
      return false;
    }

    try {
      // 更新节点属性
      const updateAttrs = {
        // 文本属性
        'text/text': style.labelText,
        'text/fontSize': style.fontSize,
        'text/fill': style.fontColor,

        // 外观属性
        'body/stroke': style.stroke,
        'body/strokeWidth': style.strokeWidth,
        'body/fill': style.fill,
        'body/rx': style.rx,
        'body/ry': style.ry
      };

      // 过滤掉未定义的属性
      Object.keys(updateAttrs).forEach(key => {
        if (updateAttrs[key] !== undefined) {
          node.attr(key, updateAttrs[key]);
        }
      });

      // 更新节点尺寸
      if (style.width !== undefined && style.height !== undefined) {
        try {
          node.resize(style.width, style.height);
        } catch (err) {
          try {
            node.size(style.width, style.height);
          } catch (err2) {
            node.attr({
              'body/width': style.width,
              'body/height': style.height
            });
          }
        }
      }

      // 更新本地状态
      const jsonData = graph.instance.toJSON();
      if (jsonData.cells) {
        graph.nodes = jsonData.cells.filter(cell => cell.shape !== 'edge');
        graph.edges = jsonData.cells.filter(cell => cell.shape === 'edge');
        graph.elements = [...graph.nodes, ...graph.edges];
      }

      return true;
    } catch (error) {
      console.error('更新节点样式失败:', error);
      return false;
    }
  };

  // 更新节点数据（角色配置等）
  const updateNodeData = (node, data) => {
    if (!node || !graph.instance) {
      console.error('更新节点数据失败: node或graph.instance为null');
      return false;
    }

    try {
      // 使用 setData 方法更新节点数据
      if (typeof node.setData === 'function') {
        const currentData = node.getData() || {};
        node.setData({ ...currentData, ...data });
      } else {
        // 备用方法：使用自定义属性
        node.roleConfig = data;
      }

      // 更新本地状态
      const jsonData = graph.instance.toJSON();
      if (jsonData.cells) {
        graph.nodes = jsonData.cells.filter(cell => cell.shape !== 'edge');
        graph.edges = jsonData.cells.filter(cell => cell.shape === 'edge');
        graph.elements = [...graph.nodes, ...graph.edges];
      }

      return true;
    } catch (error) {
      console.error('更新节点数据失败:', error);
      return false;
    }
  };

  // 更新边缘线数据（业务配置等）
  const updateEdgeData = (edge, data) => {
    if (!edge || !graph.instance) {
      console.error('更新边缘线数据失败: edge或graph.instance为null');
      return false;
    }

    try {
      // 使用 setData 方法更新边缘线数据
      if (typeof edge.setData === 'function') {
        const currentData = edge.getData() || {};
        edge.setData({ ...currentData, ...data });
      } else {
        // 备用方法：使用自定义属性
        edge.businessConfig = data;
      }

      // 更新本地状态
      const jsonData = graph.instance.toJSON();
      if (jsonData.cells) {
        graph.nodes = jsonData.cells.filter(cell => cell.shape !== 'edge');
        graph.edges = jsonData.cells.filter(cell => cell.shape === 'edge');
        graph.elements = [...graph.nodes, ...graph.edges];
      }

      return true;
    } catch (error) {
      console.error('更新边缘线数据失败:', error);
      return false;
    }
  };

  // 锁定画布功能
  const lockGraph = (locked = true) => {
    if (!graph.instance) return;

    graph.locked = locked;

    if (locked) {
      console.log('锁定画布模式启用');

      // 1. 禁用所有交互功能
      if (graph.instance.options?.interacting) {
        graph.instance.options.interacting.nodeMovable = false;        // 禁用节点拖拽
        graph.instance.options.interacting.edgeMovable = false;        // 禁用边拖拽
        graph.instance.options.interacting.arrowheadMovable = false;   // 禁用箭头拖拽
        graph.instance.options.interacting.vertexMovable = false;      // 禁用顶点拖拽
        graph.instance.options.interacting.vertexAddable = false;      // 禁用添加顶点
        graph.instance.options.interacting.vertexDeletable = false;    // 禁用删除顶点
        graph.instance.options.interacting.magnetConnectable = false;  // 禁用连接桩连接
        graph.instance.options.interacting.toolsAddable = false;       // 禁用工具添加
        graph.instance.options.interacting.useEdgeTools = false;       // 禁用边工具
      }
      // 2. 禁用连接功能 - 修改连接验证函数
      if (graph.instance.options?.connecting) {
        graph.instance.options.connecting.validateConnection = () => false;
      }
      //3.禁用插件
      SelectPlugin.value.disable();

      // 4. 移除所有节点和边的工具
      graph.instance.getNodes().forEach(node => {
        node.removeTools();
        // 修改鼠标样式为pointer表示可点击配置
        node.setAttrs({
          body: {
            ...node.getAttrs().body,
            style: {
              cursor: 'pointer'
            }
          }
        });
      });

      graph.instance.getEdges().forEach(edge => {
        edge.removeTools();
      });

      // 5. 隐藏所有连接桩
      // const container = document.getElementById('graph-container');
      // if (container) {
      //   const ports = container.querySelectorAll('.x6-port-body');
      //   ports.forEach(port => {
      //     port.style.pointerEvents = 'none';
      //     port.style.visibility = 'hidden';
      //   });
      // }

    } else {
      console.log('解锁画布模式');

      // 1. 恢复所有交互功能
      if (graph.instance.options?.interacting) {
        graph.instance.options.interacting.nodeMovable = true;
        graph.instance.options.interacting.edgeMovable = true;
        graph.instance.options.interacting.arrowheadMovable = true;
        graph.instance.options.interacting.vertexMovable = false;      // 保持禁用
        graph.instance.options.interacting.vertexAddable = false;      // 保持禁用
        graph.instance.options.interacting.vertexDeletable = false;    // 保持禁用
        graph.instance.options.interacting.magnetConnectable = true;
        graph.instance.options.interacting.toolsAddable = true;
        graph.instance.options.interacting.useEdgeTools = true;
      }

      // 2. 恢复连接功能
      if (graph.instance.options?.connecting) {
        graph.instance.options.connecting.validateConnection = ({ sourceView, targetView, sourceMagnet, targetMagnet }) => {
          // 禁止自身连接
          if (sourceView === targetView) {
            return false;
          }
          // 必须有有效的起点和终点磁铁
          if (!sourceMagnet || !targetMagnet) {
            return false;
          }
          return true;
        };
      }
      //3.恢复插件
      SelectPlugin.value.enable();
      // 4. 恢复节点样式
      graph.instance.getNodes().forEach(node => {
        node.setAttrs({
          body: {
            ...node.getAttrs().body,
            style: {
              cursor: 'move'
            }
          }
        });
      });

      // 5. 恢复连接桩显示
      // const container = document.getElementById('graph-container');
      // if (container) {
      //   const ports = container.querySelectorAll('.x6-port-body');
      //   ports.forEach(port => {
      //     port.style.pointerEvents = 'auto';
      //     port.style.visibility = 'visible';
      //   });
      // }
    }
  };

  return {
    graph,
    stencil,
    canUndo,
    canRedo,
    initGraph,
    initStencil,
    initMinimap,
    disposeGraph,
    updateElements,
    addNode,
    addEdge,
    removeElements,
    clearGraph,
    undo,
    redo,
    zoomIn,
    zoomOut,
    zoomReset,
    centerContent,
    exportToJSON,
    importJSON,
    exportAsImage: exportImage,
    getGraphJson,
    loadGraphData,
    updateEdgeStyle,
    updateNodeStyle,
    updateNodeData,
    updateEdgeData,
    lockGraph
  };
}