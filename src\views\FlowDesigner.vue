<script setup>
import { ref, onMounted, provide } from 'vue';
import FlowCanvas from '../components/flow/FlowCanvas.vue';
import FlowToolbar from '../components/flow/FlowToolbar.vue';
import { useFlowGraph } from '../composables/useFlowGraph';
import { useConfigOptions } from '../composables/useConfigOptions';
import { saveFlow, getFlow } from '../apis/api.js';
import { getUrlQuery } from '../utils/tools.js';

// 侧边栏可见状态
const sidebarVisible = ref(true);

// 流程图状态管理
const { graph, exportToJSON, clearGraph, importJSON, lockGraph } = useFlowGraph();

// 配置选项管理
const { initializeOptions } = useConfigOptions();

// 使用provide将流程图实例提供给子组件
provide('flowGraph', graph);

// 页面标题
const title = ref('X6流程图编辑器');

// 处理流程图导出为JSON
const handleExportJson = () => {
  const data = exportToJSON();
  if (!data) return;

  const json = JSON.stringify(data, null, 2);
  const blob = new Blob([json], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.download = 'flow-diagram.json';
  link.href = url;
  link.click();

  URL.revokeObjectURL(url);
};



// 处理导入JSON
const handleImportJson = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';

  input.onchange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const jsonData = JSON.parse(event.target.result);
        importJSON(jsonData);
      } catch (error) {
        console.error('解析JSON文件失败:', error);
        alert('解析JSON文件失败，请确保文件格式正确');
      }
    };
    reader.readAsText(file);
  };

  input.click();
};

// 处理清空画布
const handleClearGraph = () => {
  if (graph.instance) {
    clearGraph();
    console.log('画布已清空');
  } else {
    console.error('画布实例不存在，无法清空画布');
  }
};

// 处理保存到后端
const handleSaveToBackend = async () => {

  // 校验画布中是否有节点
  const nodes = graph.instance.getNodes();
  if (nodes.length === 0) {
    alert('请先在画布中添加节点');
    return;
  }
  try {
    // 获取流程图数据
    const data = exportToJSON();
    if (!data) {
      console.error('获取流程图数据失败');
      alert('获取流程图数据失败，请重试');
      return;
    }

    console.log('准备保存的流程图数据:', data);


    // 构建请求数据
    const requestData = {
      name: title.value,
      flowData: JSON.stringify(data),
      // 可以添加其他必要的字段，如流程ID、版本号等
      id: 1, // 示例ID，实际应用中可能需要从URL参数或其他地方获取
    };

    // 发送请求到后端
    const res = await saveFlow(requestData)
    const { code, message } = res
    if (code === '0000') {
      console.log('流程图保存成功:');
      alert('流程图保存成功');
    } else {
      console.error('流程图保存失败:');
      alert(`流程图保存失败: ${message || '未知错误'}`);
    }
  } catch (error) {
    console.error('保存流程图时发生错误:', error);
    alert(`保存流程图时发生错误: ${error.message || '未知错误'}`);
  }
};

// 处理选择节点或边
const handleSelect = (cell) => {
  console.log('选中元素:', cell);
  // 这里不需要做额外处理，因为节点和边的属性面板已经在 FlowCanvas 组件中处理
};

// 从URL参数加载流程图数据
const loadFlowFromUrl = async () => {
  try {
    // 获取URL参数
    const flowId = getUrlQuery(null, 'flowId');
    const lockMode = getUrlQuery(null, 'lock');

    console.log('URL参数:', { flowId, lockMode });

    // 如果有flowId参数，则从后端获取数据
    if (flowId) {
      console.log('开始从后端获取流程图数据, ID:', flowId);

      const response = await getFlow(flowId);
      console.log(response, '---xxxxxxxxxxxx');

      const { code, data, message } = response;

      if (code === '0000' && data) {
        console.log('成功获取流程图数据:', data);

        // 解析流程图数据
        let flowData;
        if (typeof data.flowData === 'string') {
          flowData = JSON.parse(data.flowData);
        } else {
          flowData = data.flowData;
        }

        // 导入流程图数据
        if (flowData && (flowData.nodes || flowData.edges)) {
          const importSuccess = importJSON(flowData);
          if (importSuccess) {
            console.log('流程图数据导入成功');

            // 更新页面标题
            if (data.name) {
              title.value = data.name;
              document.title = data.name;
            }
          } else {
            console.error('流程图数据导入失败');
            alert('流程图数据导入失败，请检查数据格式');
          }
        } else {
          console.warn('流程图数据为空或格式不正确');
        }
      } else {
        console.error('获取流程图数据失败:', message);
        alert(`获取流程图数据失败: ${message || '未知错误'}`);
      }
    }

    // 如果有lock参数，则锁定画布
    if (lockMode === 'true' || lockMode === '1') {
      console.log('锁定画布模式');
      lockGraph(true);

      // 隐藏侧边栏中的节点库（可选）
      sidebarVisible.value = false;
    }

  } catch (error) {
    console.error('从URL加载流程图数据时发生错误:', error);
    alert(`加载流程图数据时发生错误: ${error.message || '未知错误'}`);
  }
};

// 组件挂载时初始化图形和加载URL参数
onMounted(async () => {
  document.title = title.value;

  // 初始化配置选项（角色列表、选人策略等）
  await initializeOptions();

  // 等待一小段时间确保图形实例已初始化
  setTimeout(async () => {
    await loadFlowFromUrl();
  }, 500);
});
</script>

<template>
  <div class="flow-designer">
    <header class="flow-header">
      <div class="flow-title">
        <input
          v-model="title"
          type="text"
          disabled
          class="title-input"
          @input="document.title = title"
        />
      </div>

      <FlowToolbar
        v-model:sidebarVisible="sidebarVisible"
        @exportJson="handleExportJson"
        @importJson="handleImportJson"
        @clearGraph="handleClearGraph"
        @saveToBackend="handleSaveToBackend"
      />
    </header>

    <main class="flow-main">
      <FlowCanvas
        :sidebarVisible="sidebarVisible"
        @select="handleSelect"
      />
    </main>
  </div>
</template>

<style scoped>
.flow-designer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.flow-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #e6e9ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.flow-title {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background-color: #fff;
  border-bottom: 1px solid #e6e9ed;
  background-image: linear-gradient(to right, #ffffff, #f9fafc);
}

.title-input {
  font-size: 16px;
  font-weight: 600;
  color: #1f2329;
  border: 1px solid transparent;
  background-color: transparent;
  padding: 6px 10px;
  border-radius: 4px;
  width: 100%;
  outline: none;
  transition: all 0.3s;
  box-shadow: none;
}

.title-input:hover {
  border-color: #d9d9d9;
}

.title-input:focus {
  border-color: #4a6bbd;
  background-color: rgba(74, 107, 189, 0.05);
  box-shadow: 0 0 0 2px rgba(74, 107, 189, 0.1);
}

.flow-main {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-image: radial-gradient(#e6e8eb 1px, transparent 0);
  background-size: 20px 20px;
  background-position: -10px -10px;
}
</style>