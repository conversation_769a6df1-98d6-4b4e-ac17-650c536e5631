import request from '../utils/request.js'
const BASE_URL = '/';

/**
 * 保存流程图数据
 * @param {Object} data - 流程图数据
 * @returns {Promise} 请求结果
 */
export const saveFlow = (data = {}) => {
  return request({
    url: `${BASE_URL}saveFlow`,
    method: 'post',
    data
  })
}

/**
 * 获取流程图数据
 * @param {string|number} id - 流程图ID
 * @returns {Promise} 请求结果
 */
export const getFlow = (id) => {
  // return request({
  //   url: `${BASE_URL}getFlow`,
  //   method: 'get',
  //   params: { id }
  // })
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve({
        code: '0000',
        data: {
          name: '示例流程图',
          flowData: `{
            "nodes": [
              {
                "position": {
                  "x": 360,
                  "y": 260
                },
                "size": {
                  "width": 80,
                  "height": 80
                },
                "attrs": {
                  "text": {
                    "textWrap": {
                      "width": 60,
                      "height": 70,
                      "ellipsis": true
                    },
                    "text": "开始节点"
                  },
                  "body": {
                    "rx": 0,
                    "ry": 0,
                    "filter": "none"
                  }
                },
                "visible": true,
                "shape": "flow-start",
                "ports": {
                  "items": [
                    {
                      "id": "b98e7f09-fa5d-4b27-8a0b-142c4607f629",
                      "group": "top"
                    },
                    {
                      "id": "732280dc-32cb-48d7-a4bc-adfa111ffaa0",
                      "group": "right"
                    }
                  ]
                },
                "id": "49011f37-8b73-4361-92fd-bea003aed1e3",
                "zIndex": 1
              },
              {
                "position": {
                  "x": 573,
                  "y": 270
                },
                "size": {
                  "width": 120,
                  "height": 60
                },
                "attrs": {
                  "text": {
                    "textWrap": {
                      "width": 100,
                      "height": 50,
                      "ellipsis": true
                    },
                    "text": "处理节点"
                  },
                  "body": {
                    "filter": "none"
                  }
                },
                "visible": true,
                "shape": "flow-process",
                "ports": {
                  "items": [
                    {
                      "id": "33b5e25b-c38f-4b8c-857c-3b57a116bff4",
                      "group": "top"
                    },
                    {
                      "id": "e7b7263e-2489-46ad-9070-7fb17be86477",
                      "group": "right"
                    },
                    {
                      "id": "749505c7-de69-47fc-b9c8-fa963ffecde2",
                      "group": "left"
                    },
                    {
                      "id": "c9eb0624-c8f0-44c6-9e95-bc4063de3982",
                      "group": "bottom"
                    }
                  ]
                },
                "id": "6b9b65b0-5f7b-402c-97e7-43d59e181250",
                "zIndex": 2
              }
            ],
            "edges": [
              {
                "shape": "edge",
                "attrs": {
                  "line": {
                    "stroke": "#5F95FF",
                    "targetMarker": {
                      "name": "classic",
                      "size": 8
                    }
                  }
                },
                "id": "0f83c7ea-ed55-45ec-b8b3-8d8e0bc3338f",
                "zIndex": 0,
                "source": {
                  "cell": "49011f37-8b73-4361-92fd-bea003aed1e3",
                  "port": "732280dc-32cb-48d7-a4bc-adfa111ffaa0"
                },
                "target": {
                  "cell": "6b9b65b0-5f7b-402c-97e7-43d59e181250",
                  "port": "749505c7-de69-47fc-b9c8-fa963ffecde2"
                }
              }
            ]
          }`
        }
      })
    }, 200)
  })
}

/**
 * 获取角色列表
 * @returns {Promise} 请求结果
 */
export const getRoleList = () => {
  // return request({
  //   url: `${BASE_URL}getRoleList`,
  //   method: 'get'
  // })
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve({
        "code": "0000",
        "data": [
          { value: 'admin', label: '管理员' },
          { value: 'manager', label: '经理' },
          { value: 'employee', label: '员工' },
          { value: 'finance', label: '财务' },
          { value: 'hr', label: '人事' },
          { value: 'it', label: 'IT支持' }
        ]
      })
    })
  })
}

/**
 * 获取选人策略列表
 * @returns {Promise} 请求结果
 */
export const getAssigneeStrategies = () => {
  // return request({
  //   url: `${BASE_URL}getAssigneeStrategies`,
  //   method: 'get'
  // })
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve({
        "code": "0000",
        "data": [
          { value: 'direct', label: '直接指定' },
          { value: 'role', label: '按角色' },
          { value: 'department', label: '按部门' },
          { value: 'superior', label: '上级领导' },
          { value: 'dynamic', label: '动态计算' },
          { value: 'random', label: '随机分配' }
        ]
      })
    })
  })
}

