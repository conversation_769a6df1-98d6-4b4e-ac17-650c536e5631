// 二次封装axios 
import axios from 'axios'
import { sessionCache } from "./cache.js"
// import { sm2EncryptUuid, sm4Encrypt, sm2SignVerify, sm4Decrypt } from "@/utils/sm-crypto.js"
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: '/',
  // 超时
  timeout: 60000
})

// request拦截器
service.interceptors.request.use(config => {
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})
// 响应拦截器
const NO_AUTH_CODES = ['888', '000', '0', '999'];
service.interceptors.response.use(response => {
  //2.无权限访问页面校验
  let forbidCode = response.data.result_code || response.data.code;
  if (forbidCode && NO_AUTH_CODES.includes(forbidCode)) {
    alert('无权限访问');
  }
  return Promise.resolve(response.data)
  // return handleDecryptResData(response.data) || Promise.resolve(response.data);
}, err => {
  // 超时处理
  if (axios.isAxiosError(err)) {
    if (err.code === 'ECONNABORTED' && err.message.includes('timeout')) {
      // 处理超时错误
      alert('请求超时');
      return Promise.reject(new Error('请求超时'));
    }
  }
  // 根据状态码进行处理反馈
  if (err && err.response) {
    let status = err.response.status;
    // 状态码判断
    alert('错误状态码：' + status)
  } else {
    console.error("请求错误", err)
    return Promise.reject(err)
  }
})

export default service